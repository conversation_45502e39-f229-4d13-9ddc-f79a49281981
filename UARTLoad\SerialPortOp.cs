﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO.Ports;
using System.Threading;
using System.Windows.Forms;

namespace UARTLoad
{
    public class SerialPortOp
    {
        private readonly ManualResetEvent TimeoutObject = new ManualResetEvent(false);
        //定义 SerialPort对象
        public SerialPort  port;
       // static int buffersize = 30;   //十六进制数的大小（假设为6Byte）
        //byte[] buffer = new Byte[buffersize];   //创建缓冲区
        //初始化SerialPort对象方法.PortName为COM口名称,例如"COM1","COM2"等,注意是string类型

        public SerialPortOp()
        {
            port = new SerialPort();
        }

        public void InitCOM(string PortName, int Brate, bool parity, bool stop)
        {
           // port = new SerialPort(PortName);
            port.PortName = PortName;
            port.BaudRate = Brate;//波特率     

            if (parity == false)
                port.Parity = Parity.None;//无奇偶校验位
            else
                port.Parity = Parity.Even;//偶校验
            if (stop == false)
                port.StopBits = StopBits.None;
            else
                port.StopBits = StopBits.One;//一个停止位
            port.Handshake = Handshake.None;
            //port.Handshake = Handshake.RequestToSend;//控制协议
        }
        //打开串口的方法
        public bool OpenPort()
        {
            try
            {
                port.Open();
            }
            catch
            {
                port = new SerialPort();
                return false;
            }
            if (port.IsOpen)
            {
                Console.WriteLine("the port is opened!");
                return true;
            }
            else
            {
                MessageBox.Show("串口打开失败！");
                Console.WriteLine("failure to open the port!");
                return false;
            }
        }

        //关闭串口的方法
        public bool ClosePort()
        {
            try
            {
                port.Close();
                if (!port.IsOpen)
                {
                    Console.WriteLine("the port is already closed!");
                    return true;
                }
                else
                {
                    MessageBox.Show("串口关闭失败！");
                    return false;
                }
            }
            catch
            {
                port = new SerialPort();
                return false;
            }
        }

        //向串口发送数据
        public void SendData(byte[] sendBuf)
        {
            if (port.IsOpen)
            {
                if (sendBuf.Length == 0)
                {
                    MessageBox.Show("发送数据为空！");
                    Console.WriteLine("发送数据为空！");
                }
                else
                {
                  
                    try
                    {
                        port.WriteTimeout = 500;
                        if (sendBuf.Length >= sendBuf[1] * 256 + sendBuf[2] + 2)
                            port.Write(sendBuf, 0, sendBuf[1] * 256 + sendBuf[2] + 2);
                        if(sendBuf.Length ==sendBuf[1]+2)
                            port.Write(sendBuf, 0, sendBuf.Length);

                    }
                    catch
                    {
                        MessageBox.Show("数据发送超时！");
                    }
                }
            }
            else
            {
                Console.WriteLine("串口未打开！");
                MessageBox.Show("串口未打开！");
            }
            
        }
        private void CallBackMethod(IAsyncResult asyncresult)
        {
            //使阻塞的线程继续          
            TimeoutObject.Set();
        }
    }
}
